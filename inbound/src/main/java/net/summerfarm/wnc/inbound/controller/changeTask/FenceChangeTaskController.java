package net.summerfarm.wnc.inbound.controller.changeTask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.base.BaseController;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeRemarkDTO;
import net.summerfarm.wnc.api.changeTask.input.FenceChangeTaskCommand;
import net.summerfarm.wnc.api.changeTask.service.FenceChangeTaskService;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskDetailIdQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskIdQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.inbound.controller.changeTask.converter.FenceChangeTaskOrderVoConverter;
import net.summerfarm.wnc.inbound.controller.changeTask.converter.FenceChangeTaskVoConverter;
import net.summerfarm.wnc.inbound.controller.changeTask.vo.FenceChangeTaskOrderVO;
import net.summerfarm.wnc.inbound.controller.changeTask.vo.FenceChangeTaskVO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * Description:围栏切仓任务
 * date: 2023/8/23 10:32
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fence-change-task")
public class FenceChangeTaskController extends BaseController {

    @Resource
    private FenceChangeTaskService fenceChangeTaskService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 分页查询切仓任务列表
     * @param fenceChangeTaskPageQuery 查询
     * @return 结果
     */
    @PostMapping("/query/page")
    public CommonResult<PageInfo<FenceChangeTaskVO>> queryTaskPage(@RequestBody FenceChangeTaskPageQuery fenceChangeTaskPageQuery) {
        PageInfo<FenceChangeTaskVO> pageInfo = FenceChangeTaskVoConverter.dtoPage2VoPage(fenceChangeTaskService.queryTaskPage(fenceChangeTaskPageQuery));
        return CommonResult.ok(pageInfo);
    }

    /**
     * 分页查询切仓任务订单列表
     * @param fenceChangeTaskOrderPageQuery 查询
     * @return 结果
     */
    @PostMapping("/order/query/page")
    public CommonResult<PageInfo<FenceChangeTaskOrderVO>> queryTaskOrderPage(@RequestBody @Validated FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery) {
        PageInfo<FenceChangeTaskOrderVO> pageInfo = FenceChangeTaskOrderVoConverter.dtoPage2VoPage(fenceChangeTaskService.queryTaskOrderPage(fenceChangeTaskOrderPageQuery));
        return CommonResult.ok(pageInfo);
    }


    /**
     * 查询切仓任务详情
     * @param fenceChangeTaskIdQuery 查询
     * @return 结果
     */
    @PostMapping("/query/detail")
    public CommonResult<FenceChangeTaskVO> queryTaskDetail(@RequestBody @Validated FenceChangeTaskIdQuery fenceChangeTaskIdQuery) {
        return CommonResult.ok(FenceChangeTaskVoConverter.dto2Vo(fenceChangeTaskService.queryTaskDetail(fenceChangeTaskIdQuery)));
    }

    /**
     * 新增切仓任务-获取切仓说明信息
     * @param fenceChangeTaskCommand 命令
     * @return 结果
     */
    @PostMapping("/query/change-remark")
    public CommonResult<FenceChangeRemarkDTO> queryTaskChangeRemark(@RequestBody @Validated FenceChangeTaskCommand fenceChangeTaskCommand) {
        return CommonResult.ok(fenceChangeTaskService.queryTaskChangeRemark(fenceChangeTaskCommand));
    }

    /**
     * 新增切仓任务
     * @param fenceChangeTaskCommand 命令
     * @return 结果
     */
    @PostMapping("/upsert/add")
    @RequiresPermissions(value = {"area-manage:update", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<Void> addTask(@RequestBody @Validated FenceChangeTaskCommand fenceChangeTaskCommand) {
        StringJoiner key = new StringJoiner("-");
        key.add("fence-change-task").add(String.valueOf(fenceChangeTaskCommand.getFenceId()));
        RLock redissonLock = redissonClient.getLock(key.toString());
        try {
            if (!redissonLock.tryLock(0L, 5L, TimeUnit.SECONDS)) {
                throw new BizException("当前围栏正在操作切仓，请稍后重试");
            }
            fenceChangeTaskService.addTask(fenceChangeTaskCommand);
        } catch (InterruptedException e) {
            throw new BizException("处理过程中异常中断,请重试");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
        return CommonResult.ok();
    }

    /**
     * 取消切仓任务
     * @param fenceChangeTaskIdQuery 查询
     * @return 结果
     */
    @PostMapping("/upsert/cancel")
    @RequiresPermissions(value = {"area-manage:update", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<Void> cancelTask(@RequestBody @Validated FenceChangeTaskIdQuery fenceChangeTaskIdQuery) {
        fenceChangeTaskService.cancelTask(fenceChangeTaskIdQuery);
        return CommonResult.ok();
    }


    /**
     * 订单切仓
     * @param fenceChangeTaskDetailIdQuery 查询
     * @return 结果
     */
    @PostMapping("/upsert/order-change")
    @RequiresPermissions(value = {"area-manage:update", "SUPER_ADMIN"}, logical = Logical.OR)
    public CommonResult<Void> orderChange(@RequestBody @Validated FenceChangeTaskDetailIdQuery fenceChangeTaskDetailIdQuery) {
        fenceChangeTaskService.orderChange(fenceChangeTaskDetailIdQuery);
        return CommonResult.ok();
    }
}
