package net.summerfarm.wnc.application.service.changeTask.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.inbound.controller.changeTask.vo.CustomFenceChangeTaskDetailVO;
import net.summerfarm.wnc.application.service.changeTask.FenceChangeTaskQueryService;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 围栏切仓查询服务类
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangeTaskQueryServiceImpl implements FenceChangeTaskQueryService {

    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;

    @Override
    public CustomFenceChangeTaskDetailVO queryCustomFenceChangeTaskDetail(Long changeTaskId) {
        log.info("查询自定义围栏切仓任务详情，任务ID：{}", changeTaskId);

        // 1. 查询切仓任务基本信息
        FenceChangeTaskEntity fenceChangeTask = fenceChangeTaskRepository.queryById(changeTaskId);
        if (fenceChangeTask == null) {
            throw new BizException("切仓任务不存在");
        }

        // 2. 查询待生效、生效中的城市区域数据
        List<WncCityAreaChangeWarehouseRecordsEntity> waitRecords =
            wncCityAreaChangeWarehouseRecordsQueryRepository.selectTaskIdMapByFenceChangeTaskIdsChangeStatus(
                Collections.singletonList(changeTaskId), WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue())
                .getOrDefault(changeTaskId, Collections.emptyList());

        List<WncCityAreaChangeWarehouseRecordsEntity> effectiveRecords =
            wncCityAreaChangeWarehouseRecordsQueryRepository.selectTaskIdMapByFenceChangeTaskIdsChangeStatus(
                Collections.singletonList(changeTaskId), WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue())
                .getOrDefault(changeTaskId, Collections.emptyList());

        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaRecords = new ArrayList<>();
        cityAreaRecords.addAll(waitRecords);
        cityAreaRecords.addAll(effectiveRecords);

        if (CollectionUtils.isEmpty(cityAreaRecords)) {
            log.warn("未找到切仓任务相关的城市区域数据，任务ID：{}", changeTaskId);
            return buildEmptyResult(fenceChangeTask);
        }

        // 3. 根据变更批次号查询围栏变更数据
        Set<String> changeBatchNos = cityAreaRecords.stream()
            .map(WncCityAreaChangeWarehouseRecordsEntity::getChangeBatchNo)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(changeBatchNos)) {
            log.warn("未找到变更批次号，任务ID：{}", changeTaskId);
            return buildEmptyResult(fenceChangeTask);
        }

        // 4. 查询围栏变更记录（包含区域信息）
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = new ArrayList<>();
        for (String changeBatchNo : changeBatchNos) {
            List<WncFenceChangeRecordsEntity> records = wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo);
            if (!CollectionUtils.isEmpty(records)) {
                fenceChangeRecords.addAll(records);
            }
        }

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            log.warn("未找到围栏变更记录，任务ID：{}，变更批次号：{}", changeTaskId, changeBatchNos);
            return buildEmptyResult(fenceChangeTask);
        }

        // 5. 构建返回结果
        return buildCustomFenceChangeTaskDetailVO(fenceChangeTask, cityAreaRecords, fenceChangeRecords);
    }

    /**
     * 构建空结果
     */
    private CustomFenceChangeTaskDetailVO buildEmptyResult(FenceChangeTaskEntity fenceChangeTask) {
        CustomFenceChangeTaskDetailVO result = new CustomFenceChangeTaskDetailVO();
        result.setChangeAreaName(fenceChangeTask.getChangeAreaName());
        result.setChangeCityName(fenceChangeTask.getChangeCityName());
        if(fenceChangeTask.getType() != null){
            result.setType(fenceChangeTask.getType().getValue());
            result.setTypeDesc(fenceChangeTask.getType().getContent());
        }
        result.setCreateTime(fenceChangeTask.getCreateTime());
        result.setNewFenceAreaList(Collections.emptyList());
        result.setNewAreaGeoShapes(Collections.emptyList());
        result.setOldFenceAreaList(Collections.emptyList());
        result.setOldAreaGeoShapes(Collections.emptyList());
        return result;
    }

    /**
     * 构建自定义围栏切仓任务详情VO
     */
    private CustomFenceChangeTaskDetailVO buildCustomFenceChangeTaskDetailVO(
            FenceChangeTaskEntity fenceChangeTask,
            List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaRecords,
            List<WncFenceChangeRecordsEntity> fenceChangeRecords) {

        CustomFenceChangeTaskDetailVO result = new CustomFenceChangeTaskDetailVO();

        // 设置基本信息
        result.setChangeAreaName(fenceChangeTask.getChangeAreaName());
        result.setChangeCityName(fenceChangeTask.getChangeCityName());
        if(fenceChangeTask.getType() != null){
            result.setType(fenceChangeTask.getType().getValue());
            result.setTypeDesc(fenceChangeTask.getType().getContent());
        }
        result.setCreateTime(fenceChangeTask.getCreateTime());

        // 分离变更前后的围栏变更记录
        Map<Integer, List<WncFenceChangeRecordsEntity>> beforeAfterMap = fenceChangeRecords.stream()
            .collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceChangeStage));

        List<WncFenceChangeRecordsEntity> beforeRecords = beforeAfterMap.getOrDefault(0, Collections.emptyList());
        List<WncFenceChangeRecordsEntity> afterRecords = beforeAfterMap.getOrDefault(1, Collections.emptyList());

        // 构建变更前的数据
        result.setOldFenceAreaList(buildFenceAreaChangeTaskList(beforeRecords));
        result.setOldAreaGeoShapes(buildAreaGeoShapeList(beforeRecords));

        // 构建变更后的数据
        result.setNewFenceAreaList(buildFenceAreaChangeTaskList(afterRecords));
        result.setNewAreaGeoShapes(buildAreaGeoShapeList(afterRecords));

        return result;
    }

    /**
     * 构建围栏区域变更任务列表
     */
    private List<CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask> buildFenceAreaChangeTaskList(
            List<WncFenceChangeRecordsEntity> fenceChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return Collections.emptyList();
        }

        // 按围栏ID分组
        Map<Integer, List<WncFenceChangeRecordsEntity>> fenceIdMap = fenceChangeRecords.stream()
            .collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));

        List<CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask> result = new ArrayList<>();

        for (Map.Entry<Integer, List<WncFenceChangeRecordsEntity>> entry : fenceIdMap.entrySet()) {
            List<WncFenceChangeRecordsEntity> records = entry.getValue();
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }

            WncFenceChangeRecordsEntity firstRecord = records.get(0);
            CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask task =
                new CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask();

            task.setFenceName(firstRecord.getFenceName());
            task.setStoreName(firstRecord.getFenceStoreName());
            task.setAreaNoName(firstRecord.getFenceAreaName());

            // 收集自定义区域名称
            Set<String> customAreaNames = records.stream()
                .filter(record -> record.getAreaChangeRecords() != null)
                .flatMap(record -> record.getAreaChangeRecords().stream())
                .map(WncFenceAreaChangeRecordsEntity::getCustomAreaName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            task.setCustomAreaNameStrs(String.join(",", customAreaNames));

            // 收集仓库名称
            Set<String> warehouseNames = records.stream()
                .map(WncFenceChangeRecordsEntity::getFenceWarehouseNames)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            task.setWarehouseNames(String.join(",", warehouseNames));

            result.add(task);
        }

        return result;
    }

    /**
     * 构建区域地理形状列表
     */
    private List<CustomFenceChangeTaskDetailVO.AreaGeoShape> buildAreaGeoShapeList(
            List<WncFenceChangeRecordsEntity> fenceChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return Collections.emptyList();
        }

        List<CustomFenceChangeTaskDetailVO.AreaGeoShape> result = new ArrayList<>();

        for (WncFenceChangeRecordsEntity record : fenceChangeRecords) {
            if (record.getAreaChangeRecords() == null) {
                continue;
            }

            for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                CustomFenceChangeTaskDetailVO.AreaGeoShape geoShape =
                    new CustomFenceChangeTaskDetailVO.AreaGeoShape();

                geoShape.setCustomAreaName(areaRecord.getCustomAreaName());
                geoShape.setAreaDrawType(areaRecord.getAreaDrawType());
                geoShape.setGeoShape(areaRecord.getGeoShape());

                result.add(geoShape);
            }
        }

        return result;
    }
}
