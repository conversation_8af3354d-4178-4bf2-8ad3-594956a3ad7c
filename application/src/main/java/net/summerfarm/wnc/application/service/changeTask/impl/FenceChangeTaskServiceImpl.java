package net.summerfarm.wnc.application.service.changeTask.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeRemarkDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskOrderDTO;
import net.summerfarm.wnc.api.changeTask.input.FenceChangeTaskCommand;
import net.summerfarm.wnc.api.changeTask.service.FenceChangeTaskService;
import net.summerfarm.wnc.application.service.fence.CityAreaChangeRecordHandlerService;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskConverter;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskDetailIdQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskIdQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskValidator;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsCommandDomainService;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务接口实现
 * date: 2023/8/24 15:34
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceChangeTaskServiceImpl implements FenceChangeTaskService {

    private final FenceChangeTaskRepository fenceChangeTaskRepository;
    private final FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;

    private final FenceChangeTaskDomainService fenceChangeTaskDomainService;

    private final FenceChangeTaskValidator fenceChangeTaskValidator;

    private final FenceChangeTaskSender fenceChangeTaskSender;

    private final OfcQueryFacade ofcQueryFacade;

    private final WncCityAreaChangeWarehouseRecordsCommandDomainService wncCityAreaChangeWarehouseRecordsCommandDomainService;
    private final CityAreaChangeRecordHandlerService cityAreaChangeRecordHandlerService;
    private final WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    private final FenceRepository fenceRepository;
    private final AreaRepository areaRepository;
    private final WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;

    @Override
    public PageInfo<FenceChangeTaskDTO> queryTaskPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery) {
        PageInfo<FenceChangeTaskEntity> pageInfo = fenceChangeTaskRepository.queryTaskPage(fenceChangeTaskPageQuery);
        PageInfo<FenceChangeTaskDTO> dtoPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, dtoPageInfo);
        List<FenceChangeTaskEntity> fenceChangeTaskEntities = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        dtoPageInfo.setList(fenceChangeTaskEntities.stream().map(FenceChangeTaskConverter::entity2DTO).collect(Collectors.toList()));
        return dtoPageInfo;
    }

    @Override
    public PageInfo<FenceChangeTaskOrderDTO> queryTaskOrderPage(FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery) {
        PageInfo<FenceChangeTaskOrderEntity> pageInfo = fenceChangeTaskDetailRepository.queryTaskOrderPage(fenceChangeTaskOrderPageQuery);
        PageInfo<FenceChangeTaskOrderDTO> dtoPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, dtoPageInfo);
        List<FenceChangeTaskOrderEntity> fenceChangeTaskOrderEntities = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        List<FenceChangeTaskOrderDTO> fenceChangeTaskOrderDTOS = fenceChangeTaskOrderEntities.stream().map(FenceChangeTaskOrderConverter::entity2DTO).collect(Collectors.toList());
        // 处理相关名称的查询处理
        this.handleNameQuery(fenceChangeTaskOrderDTOS);
        dtoPageInfo.setList(fenceChangeTaskOrderDTOS);
        return dtoPageInfo;
    }

    private void handleNameQuery(List<FenceChangeTaskOrderDTO> fenceChangeTaskOrderDTOS) {
        if (CollectionUtils.isEmpty(fenceChangeTaskOrderDTOS)){
            return;
        }

        // 1. 收集所有需要查询的ID
        List<Integer> fenceIds = new ArrayList<>();
        List<Integer> storeNos = new ArrayList<>();
        List<Integer> areaNos = new ArrayList<>();

        // 收集围栏ID
        List<Integer> newFenceIds = fenceChangeTaskOrderDTOS.stream()
            .map(FenceChangeTaskOrderDTO::getNewFenceId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        List<Integer> oldFenceIds = fenceChangeTaskOrderDTOS.stream()
            .map(FenceChangeTaskOrderDTO::getOldFenceId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(newFenceIds)) {
            fenceIds.addAll(newFenceIds);
        }
        if (!CollectionUtils.isEmpty(oldFenceIds)) {
            fenceIds.addAll(oldFenceIds);
        }
        fenceIds = fenceIds.stream().distinct().collect(Collectors.toList());

        // 收集城配仓编号
        List<Integer> newStoreNos = fenceChangeTaskOrderDTOS.stream()
            .map(FenceChangeTaskOrderDTO::getNewStoreNo)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        List<Integer> oldStoreNos = fenceChangeTaskOrderDTOS.stream()
            .map(FenceChangeTaskOrderDTO::getOldStoreNo)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(newStoreNos)) {
            storeNos.addAll(newStoreNos);
        }
        if (!CollectionUtils.isEmpty(oldStoreNos)) {
            storeNos.addAll(oldStoreNos);
        }
        storeNos = storeNos.stream().distinct().collect(Collectors.toList());

        // 收集区域编号
        List<Integer> newAreaNos = fenceChangeTaskOrderDTOS.stream()
            .map(FenceChangeTaskOrderDTO::getNewAreaNo)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        List<Integer> oldAreaNos = fenceChangeTaskOrderDTOS.stream()
            .map(FenceChangeTaskOrderDTO::getOldAreaNo)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(newAreaNos)) {
            areaNos.addAll(newAreaNos);
        }
        if (!CollectionUtils.isEmpty(oldAreaNos)) {
            areaNos.addAll(oldAreaNos);
        }
        areaNos = areaNos.stream().distinct().collect(Collectors.toList());

        // 2. 批量查询名称映射
        Map<Integer, String> fenceIdToNameMap = queryFenceNames(fenceIds);
        Map<Integer, String> storeNoToNameMap = queryStoreNames(storeNos);
        Map<Integer, String> areaNoToNameMap = queryAreaNames(areaNos);

        // 3. 设置名称到DTO中
        for (FenceChangeTaskOrderDTO dto : fenceChangeTaskOrderDTOS) {
            // 设置围栏名称
            if (dto.getNewFenceId() != null) {
                dto.setNewFenceName(fenceIdToNameMap.get(dto.getNewFenceId()));
            }
            if (dto.getOldFenceId() != null) {
                dto.setOldFenceName(fenceIdToNameMap.get(dto.getOldFenceId()));
            }

            // 设置城配仓名称
            if (dto.getNewStoreNo() != null) {
                dto.setNewStoreName(storeNoToNameMap.get(dto.getNewStoreNo()));
            }
            if (dto.getOldStoreNo() != null) {
                dto.setOldStoreName(storeNoToNameMap.get(dto.getOldStoreNo()));
            }

            // 设置区域名称
            if (dto.getNewAreaNo() != null) {
                dto.setNewAreaNoName(areaNoToNameMap.get(dto.getNewAreaNo()));
            }
            if (dto.getOldAreaNo() != null) {
                dto.setOldAreaNoName(areaNoToNameMap.get(dto.getOldAreaNo()));
            }
        }
    }

    /**
     * 查询围栏名称映射
     * @param fenceIds 围栏ID集合
     * @return 围栏ID到名称的映射
     */
    private Map<Integer, String> queryFenceNames(List<Integer> fenceIds) {
        if (CollectionUtils.isEmpty(fenceIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> fenceIdToNameMap = new HashMap<>();
        try {
            // 批量查询围栏信息
            for (Integer fenceId : fenceIds) {
                FenceEntity fenceEntity = fenceRepository.queryById(fenceId);
                if (fenceEntity != null) {
                    fenceIdToNameMap.put(fenceId, fenceEntity.getFenceName());
                }
            }
        } catch (Exception e) {
            log.error("查询围栏名称失败，fenceIds: {}", fenceIds, e);
        }

        return fenceIdToNameMap;
    }

    /**
     * 查询城配仓名称映射
     * @param storeNos 城配仓编号集合
     * @return 城配仓编号到名称的映射
     */
    private Map<Integer, String> queryStoreNames(List<Integer> storeNos) {
        if (CollectionUtils.isEmpty(storeNos)) {
            return Collections.emptyMap();
        }

        try {
            return warehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos(storeNos);
        } catch (Exception e) {
            log.error("查询城配仓名称失败，storeNos: {}", storeNos, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 查询区域名称映射
     * @param areaNos 区域编号集合
     * @return 区域编号到名称的映射
     */
    private Map<Integer, String> queryAreaNames(List<Integer> areaNos) {
        if (CollectionUtils.isEmpty(areaNos)) {
            return Collections.emptyMap();
        }

        try {
            return areaRepository.queryAreaNoToNameMapByAreaNos(areaNos);
        } catch (Exception e) {
            log.error("查询区域名称失败，areaNos: {}", areaNos, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public FenceChangeTaskDTO queryTaskDetail(FenceChangeTaskIdQuery fenceChangeTaskIdQuery) {
        FenceChangeTaskEntity fenceChangeTaskEntity = fenceChangeTaskRepository.queryById(fenceChangeTaskIdQuery.getChangeTaskId());
        if (fenceChangeTaskEntity == null){
            throw new BizException("无效围栏切仓任务");
        }
        return FenceChangeTaskConverter.entity2DTO(fenceChangeTaskEntity);
    }

    @Override
    public FenceChangeRemarkDTO queryTaskChangeRemark(FenceChangeTaskCommand fenceChangeTaskCommand) {
        FenceChangeTaskEntity fenceChangeTaskEntity = FenceChangeTaskConverter.command2Entity(fenceChangeTaskCommand);
        //相关校验
        fenceChangeTaskValidator.validateFenceChangeTask(fenceChangeTaskEntity);
        return FenceChangeTaskConverter.remarkVo2DTO(fenceChangeTaskDomainService.queryTaskChangeRemark(fenceChangeTaskEntity));
    }

    @Transactional(rollbackFor =  Exception.class)
    @Override
    public void addTask(FenceChangeTaskCommand fenceChangeTaskCommand) {
        FenceChangeTaskEntity fenceChangeTaskEntity = FenceChangeTaskConverter.command2Entity(fenceChangeTaskCommand);
        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();
        fenceChangeTaskEntity.create(user.getNickname(), user.getBizUserId());
        // 创建切仓任务
        Long fenceChangeTaskId = fenceChangeTaskDomainService.createFenceChangeTask(fenceChangeTaskEntity);

        if(Objects.equals(FenceChangeTaskEnums.Type.FENCE.getValue(),fenceChangeTaskCommand.getType() )){
            // 区域切仓围栏
            Integer targetFenceId = fenceChangeTaskCommand.getTargetNo();
            Integer sourceFenceId = fenceChangeTaskCommand.getFenceId();
            List<Integer> changeAcmIds = fenceChangeTaskCommand.getChangeAcmIds();
            LocalDateTime exeTime = fenceChangeTaskEntity.getExeTime();

            cityAreaChangeRecordHandlerService.oldFenceAreaChangeFence(fenceChangeTaskId,targetFenceId,sourceFenceId,changeAcmIds,exeTime);
        }else{
            Integer targetStoreNo = fenceChangeTaskCommand.getTargetNo();
            Integer sourceFenceId = fenceChangeTaskCommand.getFenceId();
            LocalDateTime exeTime = fenceChangeTaskEntity.getExeTime();

            // 围栏切城配仓
            cityAreaChangeRecordHandlerService.oldFenceChangeStoreNo(fenceChangeTaskId,targetStoreNo,sourceFenceId,exeTime);
        }
    }

    @Transactional(rollbackFor =  Exception.class)
    @Override
    public void cancelTask(FenceChangeTaskIdQuery fenceChangeTaskIdQuery) {
        FenceChangeTaskEntity fenceChangeTaskEntity = fenceChangeTaskRepository.queryById(fenceChangeTaskIdQuery.getChangeTaskId());
        if (fenceChangeTaskEntity == null){
            throw new BizException("无效围栏切仓任务");
        }
        if (fenceChangeTaskEntity.isCancel()){
            throw new BizException("围栏切仓任务已取消");
        }
        if (!fenceChangeTaskEntity.isWaitHandle()){
            throw new BizException("围栏切仓任务不可取消");
        }
        // 自定义区域切仓任务取消
        wncCityAreaChangeWarehouseRecordsCommandDomainService.cancelFenceChangeTask(fenceChangeTaskEntity.getId());

        // 切仓任务取消
        fenceChangeTaskDomainService.cancelFenceChangeTask(fenceChangeTaskEntity);
    }

    @Override
    public void executeFenceChangeAreaHandle() {
        //查询当前时间点可执行(已过执行时间)且处于待处理的围栏切仓任务
        List<FenceChangeTaskEnums.Type> types = Arrays.asList(FenceChangeTaskEnums.Type.FENCE, FenceChangeTaskEnums.Type.STORE);
        List<FenceChangeTaskEntity> waitHandleTasks = fenceChangeTaskRepository.queryExecutableTask(FenceChangeTaskEnums.Status.WAIT,types);
        log.info("围栏切仓订单处理任务，需处理切仓任务：{}", JSON.toJSONString(waitHandleTasks));
        if (CollectionUtils.isEmpty(waitHandleTasks)){
            log.info("围栏切仓区域处理任务，无可处理切仓任务");
            return;
        }
        List<Long> fenceChangeTaskIds = waitHandleTasks.stream().map(FenceChangeTaskEntity::getId).collect(Collectors.toList());
        // 查询新模型是否有数据
        Map<Long, List<WncCityAreaChangeWarehouseRecordsEntity>> fenceChangeTaskIdMap = wncCityAreaChangeWarehouseRecordsQueryRepository.selectTaskIdMapByFenceChangeTaskIdsChangeStatus(
                fenceChangeTaskIds,WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue());

        for (FenceChangeTaskEntity waitHandleTask : waitHandleTasks) {
            if (fenceChangeTaskIdMap.containsKey(waitHandleTask.getId())){
                log.info("围栏切仓区域处理任务-任务编号：{}，已存在新模型数据，无需重复处理", waitHandleTask.getId());
                continue;
            }
            FenceChangeTaskEntity updateAreaIng = waitHandleTask.execute(FenceChangeTaskEnums.Status.AREA_CHANGE_ING);
            fenceChangeTaskRepository.update(updateAreaIng);
            try {
                fenceChangeTaskDomainService.doFenceChangeAreaHandle(waitHandleTask);
            }catch (Throwable e){
                log.error("围栏切仓任务编号：{}区域切换处理失败，失败原因：{}", waitHandleTask.getId(), e.getMessage(), e);
                FenceChangeTaskEntity updateFail = waitHandleTask.execute(FenceChangeTaskEnums.Status.FAIL);
                fenceChangeTaskRepository.update(updateFail);
                //飞书消息通知
                fenceChangeTaskSender.sendAreaFailMsg(waitHandleTask);
            }
        }
    }

    @Override
    public void executeFenceChangeOrderHandle() {
        //查询当前时间点可执行(已过执行时间)且处于待处理的围栏切仓任务
        List<FenceChangeTaskEnums.Type> types = Arrays.asList(FenceChangeTaskEnums.Type.FENCE, FenceChangeTaskEnums.Type.STORE);
        List<FenceChangeTaskEntity> waitHandleTasks = fenceChangeTaskRepository.queryExecutableTask(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING,types);
        log.info("围栏切仓订单处理任务，需处理切仓任务：{}", JSON.toJSONString(waitHandleTasks));
        if (CollectionUtils.isEmpty(waitHandleTasks)){
            log.info("围栏切仓区域处理任务，无可处理切仓任务");
            return;
        }
        List<Long> fenceChangeTaskIds = waitHandleTasks.stream().map(FenceChangeTaskEntity::getId).collect(Collectors.toList());
        // 查询新模型是否有数据
        Map<Long, List<WncCityAreaChangeWarehouseRecordsEntity>> fenceChangeTaskIdMap = wncCityAreaChangeWarehouseRecordsQueryRepository.selectTaskIdMapByFenceChangeTaskIdsChangeStatus(
                fenceChangeTaskIds,
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue());

        for (FenceChangeTaskEntity waitHandleTask : waitHandleTasks) {
            if (fenceChangeTaskIdMap.containsKey(waitHandleTask.getId())){
                log.info("围栏切仓订单处理任务-任务编号：{}，已存在新模型数据，无需重复处理", waitHandleTask.getId());
                continue;
            }
            FulfillmentQueryInput input = FulfillmentQueryInput.builder()
                    .city(waitHandleTask.getFenceCityNameStr())
                    .areas(waitHandleTask.getFenceAreasName())
                    .deliveryDateBegin(waitHandleTask.getExeTimePlus2Date()).build();
            //查询T+1之后待履约的履约单数据
            List<FulfillmentOrderDTO> waitHandleFulfillmentOrderDTOList;
            try {
                List<FulfillmentOrderDTO> fulfillmentOrderDTOList = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(input);
                waitHandleFulfillmentOrderDTOList = this.filterWaitHandleFulfillmentOrder(fulfillmentOrderDTOList, waitHandleTask);
            }catch (Throwable e){
                log.error("围栏切仓订单处理任务-任务编号：{}，调用OFC接口查询T+1之后待履约的履约单数据异常，等待下一次调度", waitHandleTask.getId(), e);
                //存在订单切仓失败 发送飞书消息通知
                fenceChangeTaskSender.sendOrderFailMsg(waitHandleTask, null);
                continue;
            }

            if (CollectionUtils.isEmpty(waitHandleFulfillmentOrderDTOList)){
                log.info("围栏切仓订单处理任务-任务编号：{}，未查询到待处理的履约单明细集合", waitHandleTask.getId());
                //无履约单需要处理可直接更新切仓任务状态为已完成
                FenceChangeTaskEntity updateComplete = waitHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED);
                fenceChangeTaskRepository.update(updateComplete);
                continue;
            }
            log.info("围栏切仓订单处理任务-任务编号：{}，待处理的履约单明细集合：{}，条数：{}", waitHandleTask.getId(),
                    JSON.toJSONString(waitHandleFulfillmentOrderDTOList), waitHandleFulfillmentOrderDTOList.size());


            Set<FenceChangeTaskOrderEntity> waitHandleTaskOrders;
            try {
                //合并履约单处理
                waitHandleTaskOrders = waitHandleFulfillmentOrderDTOList.stream().filter(e -> e.getSource() != null).map(e -> {
                    FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(e);
                    fenceChangeTaskOrderEntity.create(waitHandleTask.getId());
                    return fenceChangeTaskOrderEntity;
                }).collect(Collectors.toSet());
                log.info("围栏切仓订单处理任务-任务编号：{}，合并后的履约单明细集合：{}，条数：{}", waitHandleTask.getId(),
                        JSON.toJSONString(waitHandleFulfillmentOrderDTOList), waitHandleFulfillmentOrderDTOList.size());
                fenceChangeTaskDomainService.saveBatchDetail(waitHandleTaskOrders);
            }catch (Throwable e){
                if (e instanceof  DuplicateKeyException){
                    log.error("围栏切仓订单处理任务-任务编号：{}，切仓订单明细批量插入UK异常，无需重复处理", waitHandleTask.getId());
                    continue;
                }
                //存在订单切仓失败 发送飞书消息通知
                fenceChangeTaskSender.sendOrderFailMsg(waitHandleTask, waitHandleFulfillmentOrderDTOList.size());
                continue;
            }

            List<String> failOrders = new ArrayList<>();
            for (FenceChangeTaskOrderEntity waitHandleTaskOrder : waitHandleTaskOrders) {
                try {
                    fenceChangeTaskDomainService.doFenceChangeOrderHandle(waitHandleTask, waitHandleTaskOrder);
                }catch (Throwable e){
                    failOrders.add(waitHandleTaskOrder.getOuterOrderId());
                    log.info("围栏切仓订单处理任务-履约单处理失败，等待失败订单重试，异常原因：{}", e.getMessage(), e);
                    FenceChangeTaskOrderEntity updateOrderFail = waitHandleTaskOrder.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
                    fenceChangeTaskDetailRepository.update(updateOrderFail);
                }
            }
            //履约单处理完成需要直接更新切仓任务状态为已完成
            FenceChangeTaskEntity updateComplete = waitHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED);
            fenceChangeTaskRepository.update(updateComplete);
            if (!failOrders.isEmpty()){

                //存在订单切仓失败 发送飞书消息通知
                fenceChangeTaskSender.sendOrderFailMsg(waitHandleTask, failOrders.size());
            }
        }

    }

    private List<FulfillmentOrderDTO> filterWaitHandleFulfillmentOrder(List<FulfillmentOrderDTO> fulfillmentOrderDTOList, FenceChangeTaskEntity fenceChangeTaskEntity) {
        if (CollectionUtils.isEmpty(fulfillmentOrderDTOList)){
            return Collections.emptyList();
        }
        Integer newStoreNo = fenceChangeTaskEntity.getFenceChangeRemarkVO().getNewStoreNo();
        //过滤出执行切仓区域之前的单子
        List<FulfillmentOrderDTO> waitHandleOrders = fulfillmentOrderDTOList.stream().filter(e -> e.getFulfillConfirmTime().isBefore(fenceChangeTaskEntity.getExeTime())).collect(Collectors.toList());
        log.info("围栏切仓订单处理任务-任务编号：{}，执行切仓区域之前的履约单明细集合：{}", fenceChangeTaskEntity.getId(), JSON.toJSONString(waitHandleOrders));
        //过滤出执行切仓区域之后且非新仓履约的单子
        List<FulfillmentOrderDTO> waitHandleOrders2 = fulfillmentOrderDTOList.stream().filter(e -> !e.getFulfillConfirmTime().isBefore(fenceChangeTaskEntity.getExeTime()) && !Objects.equals(e.getStoreNo(), newStoreNo)).collect(Collectors.toList());
        log.info("围栏切仓订单处理任务-任务编号：{}，执行切仓区域之后且非新仓履约的履约单明细集合：{}", fenceChangeTaskEntity.getId(), JSON.toJSONString(waitHandleOrders2));
        waitHandleOrders.addAll(waitHandleOrders2);
        return waitHandleOrders;
    }

    @Override
    public void executeFenceChangeFailOrderHandle() {
        //查询执行时间是前一日且处于处理完成的围栏切仓任务 下切仓失败的订单明细
        List<FenceChangeTaskOrderEntity> retryableTaskDetails = fenceChangeTaskDetailRepository.queryRetryableTaskDetails();
        log.info("围栏切仓失败订单处理任务，需处理失败订单：{}", JSON.toJSONString(retryableTaskDetails));
        if (CollectionUtils.isEmpty(retryableTaskDetails)){
            log.info("围栏切仓失败订单处理任务，无可处理失败订单");
            return;
        }
        log.info("围栏切仓失败订单处理任务处理结束，失败订单明细：{}", JSON.toJSONString(retryableTaskDetails));
        List<FenceChangeTaskOrderEntity> againFailTaskDetails = new ArrayList<>();
        for (FenceChangeTaskOrderEntity retryableTaskDetail : retryableTaskDetails) {
            try {
                this.doOrderChange(retryableTaskDetail);
            }catch (Throwable e){
                againFailTaskDetails.add(retryableTaskDetail);
            }
        }
        List<String> orderNos = againFailTaskDetails.stream().map(FenceChangeTaskOrderEntity::buildUk).collect(Collectors.toList());
        log.info("围栏切仓失败订单处理任务处理结束，目前仍处理失败订单标识：{}", JSON.toJSONString(orderNos));

    }

    @Override
    public void orderChange(FenceChangeTaskDetailIdQuery fenceChangeTaskDetailIdQuery) {
        FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity = fenceChangeTaskDetailRepository.queryById(fenceChangeTaskDetailIdQuery.getChangeTaskDetailId());
        if (fenceChangeTaskOrderEntity == null){
            throw new BizException("无效围栏切仓任务订单明细ID");
        }
        this.doOrderChange(fenceChangeTaskOrderEntity);

    }

    private void doOrderChange(FenceChangeTaskOrderEntity fenceChangeTaskOrderEntity) {
        if (fenceChangeTaskOrderEntity == null){
            throw new BizException("无效围栏切仓任务订单明细");
        }
        if (fenceChangeTaskOrderEntity.isSuccess()){
            return;
        }
        FenceChangeTaskEntity fenceChangeTaskEntity = fenceChangeTaskRepository.queryById(fenceChangeTaskOrderEntity.getTaskId());
        if (fenceChangeTaskEntity == null){
            throw new BizException("无效围栏切仓任务ID");
        }
        boolean isRetryable = fenceChangeTaskOrderEntity.isRetryable(fenceChangeTaskEntity.getStatus());
        if (!isRetryable){
            throw new BizException("该围栏切仓任务订单明细不可发起重试");
        }
        try {
            fenceChangeTaskDomainService.doFenceChangeOrderHandle(fenceChangeTaskEntity, fenceChangeTaskOrderEntity);
        }catch (Throwable e){
            log.info("围栏切仓订单处理任务-履约单处理失败，订单标识：{}，异常原因：{}", fenceChangeTaskOrderEntity.buildUk(), e.getMessage(),  e);
            FenceChangeTaskOrderEntity updateOrderFail = fenceChangeTaskOrderEntity.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
            fenceChangeTaskDetailRepository.update(updateOrderFail);
            throw new BizException(e.getMessage());
        }
    }

}
