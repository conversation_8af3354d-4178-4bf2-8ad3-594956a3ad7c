# handleNameQuery方法实现文档

## 概述

`handleNameQuery` 方法用于根据ID查询对应的名称并设置到 `FenceChangeTaskOrderDTO` 实体中。该方法解决了在切仓任务订单明细中显示友好名称而不是ID的需求。

## 功能描述

该方法接收一个 `FenceChangeTaskOrderDTO` 列表，根据其中的ID字段（围栏ID、城配仓编号、区域编号）批量查询对应的名称，并将名称设置回DTO对象中。

## 实现逻辑

### 1. 数据收集阶段
```java
// 收集所有需要查询的ID
List<Integer> fenceIds = new ArrayList<>();
List<Integer> storeNos = new ArrayList<>();
List<Integer> areaNos = new ArrayList<>();
```

- **围栏ID收集**: 从 `newFenceId` 和 `oldFenceId` 字段收集
- **城配仓编号收集**: 从 `newStoreNo` 和 `oldStoreNo` 字段收集  
- **区域编号收集**: 从 `newAreaNo` 和 `oldAreaNo` 字段收集

### 2. 数据去重优化
```java
fenceIds = fenceIds.stream().distinct().collect(Collectors.toList());
```

- 使用Stream API进行去重，避免重复查询
- 过滤null值，确保查询的有效性

### 3. 批量查询阶段
```java
Map<Integer, String> fenceIdToNameMap = queryFenceNames(fenceIds);
Map<Integer, String> storeNoToNameMap = queryStoreNames(storeNos);
Map<Integer, String> areaNoToNameMap = queryAreaNames(areaNos);
```

- **围栏名称查询**: 通过 `FenceRepository.queryById()` 逐个查询
- **城配仓名称查询**: 通过 `WarehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos()` 批量查询
- **区域名称查询**: 通过 `AreaRepository.queryAreaNoToNameMapByAreaNos()` 批量查询

### 4. 名称映射阶段
```java
// 设置围栏名称
if (dto.getNewFenceId() != null) {
    dto.setNewFenceName(fenceIdToNameMap.get(dto.getNewFenceId()));
}
```

将查询到的名称设置到对应的DTO字段中：
- `newFenceId` → `newFenceName`
- `oldFenceId` → `oldFenceName`
- `newStoreNo` → `newStoreName`
- `oldStoreNo` → `oldStoreName`
- `newAreaNo` → `newAreaNoName`
- `oldAreaNo` → `oldAreaNoName`

## 辅助方法

### queryFenceNames(List<Integer> fenceIds)
- **功能**: 查询围栏名称映射
- **实现**: 循环调用 `FenceRepository.queryById()`
- **返回**: `Map<Integer, String>` 围栏ID到名称的映射

### queryStoreNames(List<Integer> storeNos)
- **功能**: 查询城配仓名称映射
- **实现**: 调用 `WarehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos()`
- **返回**: `Map<Integer, String>` 城配仓编号到名称的映射

### queryAreaNames(List<Integer> areaNos)
- **功能**: 查询区域名称映射
- **实现**: 调用 `AreaRepository.queryAreaNoToNameMapByAreaNos()`
- **返回**: `Map<Integer, String>` 区域编号到名称的映射

## 性能优化

### 1. 批量查询
- 城配仓和区域使用批量查询接口，减少数据库访问次数
- 围栏查询虽然是循环调用，但已经过去重优化

### 2. 数据去重
- 使用Stream API对ID进行去重，避免重复查询
- 过滤null值，减少无效查询

### 3. Map查找
- 使用HashMap进行O(1)时间复杂度的名称查找
- 避免在设置名称时重复查询

## 异常处理

### 1. 输入验证
```java
if (CollectionUtils.isEmpty(fenceChangeTaskOrderDTOS)){
    return;
}
```

### 2. 空集合处理
```java
if (CollectionUtils.isEmpty(fenceIds)) {
    return Collections.emptyMap();
}
```

### 3. 异常捕获
```java
try {
    return warehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos(storeNos);
} catch (Exception e) {
    log.error("查询城配仓名称失败，storeNos: {}", storeNos, e);
    return Collections.emptyMap();
}
```

## 数据流转

```
输入: List<FenceChangeTaskOrderDTO>
  ↓
1. 提取ID集合 (fenceIds, storeNos, areaNos)
  ↓
2. 去重优化 (distinct, filter null)
  ↓
3. 批量查询 (Repository calls)
  ↓
4. 构建映射 (Map<Integer, String>)
  ↓
5. 设置名称 (dto.setXxxName())
  ↓
输出: 已填充名称字段的DTO列表
```

## 使用场景

该方法主要用于 `queryTaskOrderPage` 方法中，在返回切仓任务订单明细分页数据前，将ID转换为用户友好的名称显示。

```java
List<FenceChangeTaskOrderDTO> fenceChangeTaskOrderDTOS = fenceChangeTaskOrderEntities.stream()
    .map(FenceChangeTaskOrderConverter::entity2DTO)
    .collect(Collectors.toList());
    
// 处理相关名称的查询处理
this.handleNameQuery(fenceChangeTaskOrderDTOS);
```

## 扩展性

如果需要添加新的ID到名称的映射：

1. 在数据收集阶段添加新的ID收集逻辑
2. 创建对应的查询方法
3. 在批量查询阶段调用新的查询方法
4. 在名称映射阶段添加设置逻辑

## 总结

该实现通过批量查询、数据去重、异常处理等优化手段，高效地完成了ID到名称的转换功能，提升了用户体验，同时保证了系统的性能和稳定性。
